# Student Management System
## Institute of Comprehensive Vocational Skills - Ajkhara

A comprehensive system for managing student data, grades, and generating certificates and reports.

## Requirements

- Python 3.6 or newer
- tkinter (included with Python by default)
- sqlite3 (included with Python by default)

## How to Run

### Method 1 (Easiest):
Double-click: `run.bat`

### Method 2:
```bash
python run_student_system.py
```

### Method 3:
```bash
cd student_management_system
python main.py
```

## Troubleshooting

If the program doesn't work:
1. Run diagnostic: `python تشخيص_المشاكل.py`
2. Run quick fix: `python إصلاح_سريع.py`
3. Check `QUICK_START.txt` for solutions

## الميزات الرئيسية

### 1. إدارة بيانات الطلاب
- إضافة طلاب جدد
- عرض قوائم الطلاب حسب الشعبة
- تعديل بيانات الطلاب
- تو<PERSON>يد أرقام الجلوس تلقائياً

### 2. إد<PERSON><PERSON>ة الدرجات
- إدخال درجات الفصل الأول والثاني
- إدخال درجات الدور الثاني
- حساب النتائج تلقائياً
- عرض تقارير الدرجات

### 3. البحث والاستعلام
- البحث بالاسم أو الرقم الوطني أو رقم الجلوس
- عرض درجات الطالب
- إحصائيات سريعة

### 4. إنتاج الشهادات
- شهادات النجاح
- شهادات الدرجات
- معاينة قبل الطباعة
- حفظ بصيغ متعددة

### 5. بطاقات أرقام الجلوس
- إنتاج بطاقات الجلوس للامتحانات
- تخصيص معلومات الامتحان
- طباعة مجمعة

### 6. إعدادات النظام
- إعدادات المعهد
- النسخ الاحتياطي
- إدارة البيانات
- إضافة شعب جديدة

## هيكل المشروع

```
student_management_system/
├── main.py                 # الملف الرئيسي
├── database/
│   └── db_manager.py      # إدارة قاعدة البيانات
├── gui/
│   ├── main_window.py     # النافذة الرئيسية
│   ├── student_entry.py   # إدخال بيانات الطلاب
│   ├── grade_entry.py     # إدخال الدرجات
│   ├── student_search.py  # البحث عن الطلاب
│   ├── certificate_generator.py  # مولد الشهادات
│   ├── seat_cards_generator.py   # مولد بطاقات الجلوس
│   └── settings_window.py # نافذة الإعدادات
├── utils/
│   └── arabic_support.py  # دعم اللغة العربية
└── data/
    └── student_data.db    # قاعدة البيانات (تُنشأ تلقائياً)
```

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite مع الجداول التالية:

- **students**: بيانات الطلاب
- **sections**: الشعب الدراسية
- **subjects**: المواد الدراسية
- **grades**: درجات الطلاب

## الاستخدام

### إضافة طالب جديد:
1. اضغط على "إدخال بيانات الطلاب"
2. املأ البيانات المطلوبة
3. اختر الشعبة
4. اضغط "حفظ"

### إدخال الدرجات:
1. اضغط على "إدخال الدرجات"
2. اختر الشعبة والمادة
3. اضغط "تحميل الطلاب"
4. أدخل الدرجات
5. اضغط "حفظ الدرجات"

### البحث عن طالب:
1. اضغط على "البحث عن طالب"
2. أدخل الاسم أو الرقم الوطني أو رقم الجلوس
3. اضغط "بحث"
4. انقر نقراً مزدوجاً لعرض الدرجات

### إنتاج الشهادات:
1. اضغط على "إنتاج الشهادات"
2. اختر الشعبة والسنة الدراسية
3. حدد الطلاب المطلوبين
4. اضغط "إنتاج الشهادات"

## النسخ الاحتياطي

- يمكن إنشاء نسخ احتياطية من الإعدادات
- يُنصح بعمل نسخة احتياطية دورياً
- يمكن استعادة البيانات من النسخ الاحتياطية

## الدعم الفني

للمساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع إدارة النظام.

## الترخيص

هذا النظام مطور خصيصاً لمعهد المتوسط للمهن الشاملة - اجخرة.

---

**ملاحظة**: تأكد من وجود Python مثبت على النظام قبل التشغيل.
