#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام إدارة الطلاب
Student Management System Launcher
"""

import sys
import os

# إضافة مسار المشروع
project_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'student_management_system')
sys.path.insert(0, project_path)

try:
    from main import main
    
    if __name__ == "__main__":
        print("=" * 50)
        print("نظام إدارة الطلاب")
        print("معهد المتوسط للمهن الشاملة - اجخرة")
        print("=" * 50)
        main()
        
except ImportError as e:
    print(f"خطأ في استيراد الملفات: {e}")
    print("تأكد من وجود جميع ملفات النظام")
    input("اضغط Enter للخروج...")
except Exception as e:
    print(f"خطأ في تشغيل النظام: {e}")
    input("اضغط Enter للخروج...")
