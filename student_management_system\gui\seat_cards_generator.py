#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد بطاقات أرقام الجلوس
Seat Cards Generator
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sys
import os
from datetime import datetime

# إضافة مسار المكتبات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.arabic_support import *

class SeatCardsGenerator:
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.setup_ui()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("مولد بطاقات أرقام الجلوس")
        self.window.geometry("800x600")
        self.window.configure(bg='#ecf0f1')
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg='#2c3e50', height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="مولد بطاقات أرقام الجلوس",
            font=get_arabic_font(size=18, weight='bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(pady=15)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg='#ecf0f1')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # إطار اختيار الشعبة
        section_frame = tk.LabelFrame(
            main_frame,
            text="اختيار الشعبة",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        section_frame.pack(fill='x', pady=(0, 20))
        
        section_content = tk.Frame(section_frame, bg='#ecf0f1')
        section_content.pack(fill='x', padx=20, pady=15)
        
        tk.Label(
            section_content,
            text="الشعبة:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            fg='#2c3e50'
        ).pack(side='right', padx=(0, 10))
        
        self.section_var = tk.StringVar()
        self.section_combo = ttk.Combobox(
            section_content,
            textvariable=self.section_var,
            font=get_arabic_font(size=12),
            state='readonly',
            width=30
        )
        self.section_combo.pack(side='right', padx=(0, 10))
        self.section_combo.bind('<<ComboboxSelected>>', self.on_section_changed)
        
        # زر تحديث
        refresh_btn = create_arabic_button(
            section_content,
            "تحديث",
            command=self.load_sections,
            bg='#3498db',
            fg='white',
            width=10
        )
        refresh_btn.pack(side='left')
        
        # تحميل الشعب
        self.load_sections()
        
        # إطار معلومات الامتحان
        exam_frame = tk.LabelFrame(
            main_frame,
            text="معلومات الامتحان",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        exam_frame.pack(fill='x', pady=(0, 20))
        
        exam_content = tk.Frame(exam_frame, bg='#ecf0f1')
        exam_content.pack(fill='x', padx=20, pady=15)
        
        # السنة الدراسية
        year_frame = tk.Frame(exam_content, bg='#ecf0f1')
        year_frame.pack(fill='x', pady=5)
        
        tk.Label(
            year_frame,
            text="السنة الدراسية:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            fg='#2c3e50'
        ).pack(side='right', padx=(0, 10))
        
        self.year_var = tk.StringVar()
        current_year = datetime.now().year
        self.year_var.set(f"{current_year}-{current_year + 1}")
        
        year_entry = RTLEntry(
            year_frame,
            textvariable=self.year_var,
            font=get_arabic_font(size=12),
            width=20
        )
        year_entry.pack(side='right')
        
        # نوع الامتحان
        exam_type_frame = tk.Frame(exam_content, bg='#ecf0f1')
        exam_type_frame.pack(fill='x', pady=5)
        
        tk.Label(
            exam_type_frame,
            text="نوع الامتحان:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            fg='#2c3e50'
        ).pack(side='right', padx=(0, 10))
        
        self.exam_type_var = tk.StringVar(value="امتحان نهاية الفصل الأول")
        exam_type_combo = ttk.Combobox(
            exam_type_frame,
            textvariable=self.exam_type_var,
            font=get_arabic_font(size=12),
            values=[
                "امتحان نهاية الفصل الأول",
                "امتحان نهاية الفصل الثاني",
                "امتحان الدور الثاني",
                "امتحان نهاية السنة"
            ],
            width=25
        )
        exam_type_combo.pack(side='right')
        
        # إطار الطلاب
        students_frame = tk.LabelFrame(
            main_frame,
            text="قائمة الطلاب",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        students_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        # جدول الطلاب
        self.create_students_table(students_frame)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg='#ecf0f1')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        generate_btn = create_arabic_button(
            buttons_frame,
            "إنتاج البطاقات",
            command=self.generate_cards,
            bg='#27ae60',
            fg='white',
            width=15
        )
        generate_btn.pack(side='right', padx=(0, 10))
        
        preview_btn = create_arabic_button(
            buttons_frame,
            "معاينة",
            command=self.preview_card,
            bg='#3498db',
            fg='white',
            width=10
        )
        preview_btn.pack(side='right', padx=(0, 10))
        
        select_all_btn = create_arabic_button(
            buttons_frame,
            "تحديد الكل",
            command=self.select_all_students,
            bg='#9b59b6',
            fg='white',
            width=10
        )
        select_all_btn.pack(side='right', padx=(0, 10))
        
        close_btn = create_arabic_button(
            buttons_frame,
            "إغلاق",
            command=self.window.destroy,
            bg='#e74c3c',
            fg='white',
            width=10
        )
        close_btn.pack(side='left')
    
    def create_students_table(self, parent):
        """إنشاء جدول الطلاب"""
        table_frame = tk.Frame(parent, bg='#ecf0f1')
        table_frame.pack(fill='both', expand=True, padx=20, pady=15)
        
        # الأعمدة
        columns = ('اختيار', 'الاسم الكامل', 'رقم الجلوس', 'الرقم الوطني')
        
        self.students_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=12
        )
        
        # تكوين الأعمدة
        self.students_tree.heading('اختيار', text='اختيار', anchor='center')
        self.students_tree.column('اختيار', width=80, anchor='center')
        
        self.students_tree.heading('الاسم الكامل', text='الاسم الكامل', anchor='center')
        self.students_tree.column('الاسم الكامل', width=250, anchor='center')
        
        self.students_tree.heading('رقم الجلوس', text='رقم الجلوس', anchor='center')
        self.students_tree.column('رقم الجلوس', width=120, anchor='center')
        
        self.students_tree.heading('الرقم الوطني', text='الرقم الوطني', anchor='center')
        self.students_tree.column('الرقم الوطني', width=150, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.students_tree.yview)
        self.students_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.students_tree.pack(side='right', fill='both', expand=True)
        scrollbar.pack(side='left', fill='y')
        
        # ربط النقر للاختيار
        self.students_tree.bind('<Button-1>', self.on_student_click)
    
    def load_sections(self):
        """تحميل الشعب"""
        try:
            sections = self.db_manager.get_all_sections()
            self.section_combo['values'] = sections
            if sections:
                self.section_combo.set(sections[0])
                self.on_section_changed()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الشعب: {str(e)}")
    
    def on_section_changed(self, event=None):
        """عند تغيير الشعبة"""
        self.load_students()
    
    def load_students(self):
        """تحميل الطلاب"""
        section = self.section_var.get()
        if not section:
            return
        
        try:
            # مسح البيانات السابقة
            for item in self.students_tree.get_children():
                self.students_tree.delete(item)
            
            # تحميل الطلاب
            students = self.db_manager.get_students_by_section(section)
            
            for student in students:
                self.students_tree.insert('', 'end', values=(
                    "☐",  # مربع الاختيار
                    student[1],  # الاسم الكامل
                    student[3],  # رقم الجلوس
                    student[2]   # الرقم الوطني
                ), tags=(student[0],))  # معرف الطالب
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الطلاب: {str(e)}")
    
    def on_student_click(self, event):
        """معالجة النقر على طالب"""
        region = self.students_tree.identify_region(event.x, event.y)
        if region == "cell":
            column = self.students_tree.identify_column(event.x, event.y)
            if column == "#1":  # عمود الاختيار
                item = self.students_tree.identify_row(event.y)
                if item:
                    current_values = list(self.students_tree.item(item, 'values'))
                    # تبديل حالة الاختيار
                    current_values[0] = "☑" if current_values[0] == "☐" else "☐"
                    self.students_tree.item(item, values=current_values)
    
    def select_all_students(self):
        """تحديد جميع الطلاب"""
        for item in self.students_tree.get_children():
            current_values = list(self.students_tree.item(item, 'values'))
            current_values[0] = "☑"
            self.students_tree.item(item, values=current_values)
    
    def get_selected_students(self):
        """الحصول على الطلاب المحددين"""
        selected = []
        for item in self.students_tree.get_children():
            values = self.students_tree.item(item, 'values')
            if values[0] == "☑":
                student_id = self.students_tree.item(item, 'tags')[0]
                selected.append({
                    'id': student_id,
                    'name': values[1],
                    'seat_number': values[2],
                    'national_id': values[3]
                })
        return selected
    
    def preview_card(self):
        """معاينة البطاقة"""
        selected_students = self.get_selected_students()
        if not selected_students:
            messagebox.showwarning("تحذير", "يرجى اختيار طالب واحد على الأقل")
            return
        
        # معاينة الطالب الأول
        student = selected_students[0]
        self.show_card_preview(student)
    
    def show_card_preview(self, student):
        """عرض معاينة البطاقة"""
        preview_window = tk.Toplevel(self.window)
        preview_window.title("معاينة بطاقة رقم الجلوس")
        preview_window.geometry("400x300")
        preview_window.configure(bg='white')
        preview_window.transient(self.window)
        
        # إطار البطاقة
        card_frame = tk.Frame(preview_window, bg='white', relief='solid', bd=2)
        card_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # رأس البطاقة
        header_text = "معهد المتوسط للمهن الشاملة - اجخرة\nبطاقة رقم الجلوس"
        header_label = tk.Label(
            card_frame,
            text=header_text,
            font=get_arabic_font(size=14, weight='bold'),
            bg='white',
            fg='#2c3e50',
            justify='center'
        )
        header_label.pack(pady=20)
        
        # معلومات الطالب
        info_text = f"""
الاسم: {student['name']}
رقم الجلوس: {student['seat_number']}
الرقم الوطني: {student['national_id']}

السنة الدراسية: {self.year_var.get()}
{self.exam_type_var.get()}
        """
        
        info_label = tk.Label(
            card_frame,
            text=info_text,
            font=get_arabic_font(size=12),
            bg='white',
            fg='#2c3e50',
            justify='center'
        )
        info_label.pack(pady=20)
        
        # تاريخ الإصدار
        date_text = f"تاريخ الإصدار: {datetime.now().strftime('%Y/%m/%d')}"
        date_label = tk.Label(
            card_frame,
            text=date_text,
            font=get_arabic_font(size=10),
            bg='white',
            fg='#7f8c8d'
        )
        date_label.pack(side='bottom', pady=10)
        
        # زر الإغلاق
        close_btn = create_arabic_button(
            preview_window,
            "إغلاق",
            command=preview_window.destroy,
            bg='#e74c3c',
            fg='white'
        )
        close_btn.pack(pady=10)
    
    def generate_cards(self):
        """إنتاج البطاقات"""
        selected_students = self.get_selected_students()
        if not selected_students:
            messagebox.showwarning("تحذير", "يرجى اختيار طالب واحد على الأقل")
            return
        
        # اختيار مجلد الحفظ
        save_dir = filedialog.askdirectory(title="اختيار مجلد حفظ البطاقات")
        if not save_dir:
            return
        
        try:
            count = 0
            for student in selected_students:
                # إنشاء محتوى البطاقة
                card_content = f"""
معهد المتوسط للمهن الشاملة - اجخرة
بطاقة رقم الجلوس

الاسم: {student['name']}
رقم الجلوس: {student['seat_number']}
الرقم الوطني: {student['national_id']}

السنة الدراسية: {self.year_var.get()}
{self.exam_type_var.get()}

تاريخ الإصدار: {datetime.now().strftime('%Y/%m/%d')}

ملاحظات:
- يجب إحضار هذه البطاقة يوم الامتحان
- يجب إحضار الهوية الشخصية
- الحضور قبل موعد الامتحان بـ 30 دقيقة
                """
                
                # حفظ الملف
                filename = f"بطاقة_جلوس_{student['seat_number']}_{student['name']}.txt"
                # إزالة الأحرف غير المسموحة من اسم الملف
                filename = "".join(c for c in filename if c.isalnum() or c in (' ', '-', '_', '.', 'أ', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص', 'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي', 'ء', 'آ', 'إ', 'ئ', 'ؤ', 'ة'))
                filepath = os.path.join(save_dir, filename)
                
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(card_content)
                
                count += 1
            
            messagebox.showinfo("تم الإنتاج", f"تم إنتاج {count} بطاقة بنجاح في:\n{save_dir}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنتاج البطاقات: {str(e)}")

# نقطة تشغيل مستقلة لاختبار النافذة
if __name__ == "__main__":
    class DummyDB:
        def get_all_sections(self):
            return ["الشعبة الأولى", "الشعبة الثانية"]
        
        def get_students_by_section(self, section):
            return [
                (1, "أحمد محمد علي", "1234567890", "A001", "2000-01-01", "ذكر", "123456789"),
                (2, "سارة أحمد محمد", "0987654321", "A002", "2001-02-02", "أنثى", "987654321"),
            ]

    root = tk.Tk()
    root.withdraw()
    app = SeatCardsGenerator(root, DummyDB())
    root.mainloop()
