#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
دعم اللغة العربية والخطوط
Arabic Language and Font Support
"""

import tkinter as tk
from tkinter import font
import platform

def setup_arabic_font(root):
    """إعداد الخط العربي للتطبيق"""
    system = platform.system()
    
    # تحديد الخط المناسب حسب نظام التشغيل
    if system == "Windows":
        arabic_fonts = ["Tahoma", "Arial Unicode MS", "Segoe UI", "Arial"]
    elif system == "Darwin":  # macOS
        arabic_fonts = ["Al Bayan", "Arial Unicode MS", "Helvetica"]
    else:  # Linux
        arabic_fonts = ["DejaVu Sans", "Liberation Sans", "Arial"]
    
    # البحث عن خط متاح
    available_font = None
    for font_name in arabic_fonts:
        try:
            test_font = font.Font(family=font_name, size=12)
            available_font = font_name
            break
        except:
            continue
    
    if not available_font:
        available_font = "Arial"
    
    # تعيين الخطوط الافتراضية
    default_font = font.nametofont("TkDefaultFont")
    default_font.configure(family=available_font, size=11)
    
    text_font = font.nametofont("TkTextFont")
    text_font.configure(family=available_font, size=11)
    
    fixed_font = font.nametofont("TkFixedFont")
    fixed_font.configure(family=available_font, size=10)
    
    return available_font

def get_arabic_font(size=11, weight="normal"):
    """الحصول على خط عربي بحجم ووزن محددين"""
    system = platform.system()
    
    if system == "Windows":
        font_family = "Tahoma"
    elif system == "Darwin":
        font_family = "Al Bayan"
    else:
        font_family = "DejaVu Sans"
    
    return font.Font(family=font_family, size=size, weight=weight)

def configure_rtl_text(text_widget):
    """تكوين النص للكتابة من اليمين لليسار"""
    try:
        # محاولة تعيين اتجاه النص
        text_widget.configure(justify='right')
        text_widget.tag_configure("rtl", justify='right')
        text_widget.tag_add("rtl", "1.0", "end")
    except:
        pass

def format_arabic_number(number):
    """تحويل الأرقام الإنجليزية إلى عربية"""
    arabic_digits = "٠١٢٣٤٥٦٧٨٩"
    english_digits = "0123456789"
    
    result = str(number)
    for i, digit in enumerate(english_digits):
        result = result.replace(digit, arabic_digits[i])
    
    return result

def format_english_number(arabic_number):
    """تحويل الأرقام العربية إلى إنجليزية"""
    arabic_digits = "٠١٢٣٤٥٦٧٨٩"
    english_digits = "0123456789"
    
    result = str(arabic_number)
    for i, digit in enumerate(arabic_digits):
        result = result.replace(digit, english_digits[i])
    
    return result

class RTLEntry(tk.Entry):
    """حقل إدخال يدعم الكتابة من اليمين لليسار"""
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.configure(justify='right')
        self.bind('<KeyPress>', self.on_key_press)
    
    def on_key_press(self, event):
        """معالجة الضغط على المفاتيح"""
        # يمكن إضافة معالجة خاصة للنص العربي هنا
        pass

class RTLText(tk.Text):
    """مربع نص يدعم الكتابة من اليمين لليسار"""
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        configure_rtl_text(self)

def create_arabic_label(parent, text, **kwargs):
    """إنشاء تسمية عربية"""
    default_kwargs = {
        'font': get_arabic_font(),
        'anchor': 'e',  # محاذاة لليمين
        'justify': 'right'
    }
    default_kwargs.update(kwargs)
    
    return tk.Label(parent, text=text, **default_kwargs)

def create_arabic_button(parent, text, **kwargs):
    """إنشاء زر بنص عربي"""
    default_kwargs = {
        'font': get_arabic_font(weight='bold')
    }
    default_kwargs.update(kwargs)
    
    return tk.Button(parent, text=text, **default_kwargs)

def validate_arabic_input(text):
    """التحقق من صحة النص العربي"""
    # السماح بالأحرف العربية والمسافات والأرقام
    allowed_chars = set('أبتثجحخدذرزسشصضطظعغفقكلمنهويءآإئؤة ابتثجحخدذرزسشصضطظعغفقكلمنهوي0123456789٠١٢٣٤٥٦٧٨٩ ')
    return all(char in allowed_chars for char in text)

def format_date_arabic(date_str):
    """تنسيق التاريخ بالعربية"""
    months = {
        '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
        '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
        '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
    }
    
    try:
        parts = date_str.split('-')
        if len(parts) == 3:
            year, month, day = parts
            month_name = months.get(month, month)
            return f"{day} {month_name} {year}"
    except:
        pass
    
    return date_str
