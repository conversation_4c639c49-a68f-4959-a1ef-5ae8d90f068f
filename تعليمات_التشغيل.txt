تعليمات تشغيل نظام إدارة الطلاب
=====================================

طرق التشغيل:
-----------

1. الطريقة الأسهل (Windows):
   - انقر نقراً مزدوجاً على ملف: تشغيل_نظام_الطلاب.bat

2. من سطر الأوامر:
   - افتح Command Prompt أو PowerShell
   - انتقل إلى مجلد البرنامج
   - اكتب: python run_student_system.py

3. تشغيل مباشر:
   - انتقل إلى مجلد student_management_system
   - اكتب: python main.py

المتطلبات:
----------
- Python 3.6 أو أحدث
- جميع المكتبات مثبتة افتراضياً مع Python

استكشاف الأخطاء:
----------------

إذا ظهرت رسالة "python is not recognized":
- تأكد من تثبيت Python
- أضف Python إلى متغير PATH

إذا لم تظهر النافذة:
- تحقق من رسائل الخطأ في سطر الأوامر
- تأكد من وجود جميع الملفات

إذا ظهر خطأ في قاعدة البيانات:
- تأكد من صلاحيات الكتابة في المجلد
- احذف ملف data/student_data.db وأعد التشغيل

الاستخدام الأول:
---------------
1. شغل البرنامج
2. اذهب إلى "إعدادات النظام"
3. أدخل معلومات المعهد
4. أضف الشعب المطلوبة
5. ابدأ بإدخال بيانات الطلاب

النسخ الاحتياطي:
---------------
- اذهب إلى "إعدادات النظام" > "النسخ الاحتياطي"
- حدد مجلد الحفظ
- اضغط "إنشاء نسخة احتياطية"

للمساعدة:
----------
راجع ملف README.md للتفاصيل الكاملة
