@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - معهد المتوسط للمهن الشاملة
cls
echo ================================================
echo نظام إدارة الطلاب
echo معهد المتوسط للمهن الشاملة - اجخرة
echo ================================================
echo.
echo جاري تشغيل النظام...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python من https://python.org
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود الملف الرئيسي
if not exist "run_student_system.py" (
    echo خطأ: ملف run_student_system.py غير موجود
    echo تأكد من وجود جميع ملفات البرنامج
    echo.
    pause
    exit /b 1
)

REM تشغيل البرنامج
echo تم العثور على Python، جاري تشغيل النظام...
echo.
python run_student_system.py

REM التحقق من حالة الخروج
if %errorlevel% neq 0 (
    echo.
    echo ================================================
    echo حدث خطأ في تشغيل النظام
    echo ================================================
    echo.
    echo الأسباب المحتملة:
    echo 1. ملفات البرنامج مفقودة أو تالفة
    echo 2. مشكلة في صلاحيات الوصول
    echo 3. مشكلة في إعدادات Python
    echo.
    echo الحلول المقترحة:
    echo 1. تأكد من وجود جميع ملفات البرنامج
    echo 2. شغل البرنامج كمدير
    echo 3. أعد تثبيت Python
    echo.
    pause
) else (
    echo.
    echo تم إنهاء البرنامج بنجاح
)
