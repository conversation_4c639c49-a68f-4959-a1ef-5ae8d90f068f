@echo off
chcp 65001 >nul
title Student Management System - Institute of Comprehensive Vocational Skills
cls
echo ================================================
echo Student Management System
echo Institute of Comprehensive Vocational Skills - Ajkhara
echo ================================================
echo.
echo Starting the system...
echo.

REM Check Python installation
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

REM Check main file exists
if not exist "run_student_system.py" (
    echo Error: run_student_system.py file not found
    echo Make sure all program files are present in the current directory
    echo.
    pause
    exit /b 1
)

REM Display Python version
echo Python installation found:
python --version
echo.

REM Run the program
echo Starting Student Management System...
echo.
python run_student_system.py

REM Check exit status
if %errorlevel% neq 0 (
    echo.
    echo ================================================
    echo ERROR: System failed to start
    echo ================================================
    echo.
    echo Possible causes:
    echo 1. Program files missing or corrupted
    echo 2. Permission issues
    echo 3. Python configuration problems
    echo 4. Missing dependencies
    echo.
    echo Suggested solutions:
    echo 1. Run diagnostic tool: python تشخيص_المشاكل.py
    echo 2. Run quick fix: python إصلاح_سريع.py
    echo 3. Run as administrator
    echo 4. Reinstall Python with tkinter support
    echo.
    echo For detailed help, check: تعليمات_التشغيل.txt
    echo.
    pause
) else (
    echo.
    echo ================================================
    echo Program ended successfully
    echo ================================================
)
