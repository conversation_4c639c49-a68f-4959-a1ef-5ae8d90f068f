#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات - نظام إدارة الطلاب
Database Manager for Student Management System
"""

import sqlite3
import os
from datetime import datetime
import hashlib

class DatabaseManager:
    def __init__(self, db_path="student_data.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول الطلاب
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS students (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                full_name TEXT NOT NULL,
                national_id TEXT UNIQUE NOT NULL,
                seat_number TEXT UNIQUE NOT NULL,
                section TEXT NOT NULL,
                birth_date TEXT,
                gender TEXT,
                phone TEXT,
                created_date TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الشعب
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                section_name TEXT UNIQUE NOT NULL,
                created_date TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المواد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS subjects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                subject_name TEXT NOT NULL,
                section_id INTEGER,
                created_date TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (section_id) REFERENCES sections (id)
            )
        ''')
        
        # جدول الدرجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS grades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER,
                subject_id INTEGER,
                semester1_work REAL DEFAULT 0,
                semester1_exam REAL DEFAULT 0,
                semester2_work REAL DEFAULT 0,
                semester2_exam REAL DEFAULT 0,
                second_round_exam REAL DEFAULT 0,
                is_passed BOOLEAN DEFAULT 0,
                passed_in_second_round BOOLEAN DEFAULT 0,
                academic_year TEXT,
                created_date TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES students (id),
                FOREIGN KEY (subject_id) REFERENCES subjects (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # إضافة بيانات افتراضية
        self.add_default_data()
    
    def add_default_data(self):
        """إضافة بيانات افتراضية للنظام"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # إضافة شعب افتراضية
        default_sections = [
            "الشعبة الأولى",
            "الشعبة الثانية", 
            "الشعبة الثالثة"
        ]
        
        for section in default_sections:
            cursor.execute('''
                INSERT OR IGNORE INTO sections (section_name) VALUES (?)
            ''', (section,))
        
        # إضافة مواد افتراضية
        default_subjects = [
            "الرياضيات",
            "الفيزياء",
            "الكيمياء",
            "اللغة العربية",
            "اللغة الإنجليزية",
            "الحاسوب",
            "التقنية المهنية"
        ]
        
        # الحصول على معرف الشعبة الأولى
        cursor.execute("SELECT id FROM sections WHERE section_name = ?", ("الشعبة الأولى",))
        section_id = cursor.fetchone()
        
        if section_id:
            for subject in default_subjects:
                cursor.execute('''
                    INSERT OR IGNORE INTO subjects (subject_name, section_id) VALUES (?, ?)
                ''', (subject, section_id[0]))
        
        conn.commit()
        conn.close()
    
    def generate_seat_number(self, full_name, section):
        """توليد رقم الجلوس بناءً على الاسم والشعبة"""
        # إنشاء hash من الاسم والشعبة لضمان الثبات
        name_hash = hashlib.md5(f"{full_name}{section}".encode('utf-8')).hexdigest()[:6]
        
        # الحصول على عدد الطلاب في الشعبة
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM students WHERE section = ?", (section,))
        count = cursor.fetchone()[0]
        conn.close()
        
        # تكوين رقم الجلوس
        seat_number = f"{section[:2]}{count + 1:03d}{name_hash[:3].upper()}"
        return seat_number
    
    def add_student(self, student_data):
        """إضافة طالب جديد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # توليد رقم الجلوس
            seat_number = self.generate_seat_number(student_data['full_name'], student_data['section'])
            
            cursor.execute('''
                INSERT INTO students (full_name, national_id, seat_number, section, 
                                    birth_date, gender, phone)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                student_data['full_name'],
                student_data['national_id'],
                seat_number,
                student_data['section'],
                student_data['birth_date'],
                student_data['gender'],
                student_data['phone']
            ))
            
            student_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return student_id, seat_number
            
        except sqlite3.IntegrityError as e:
            if "national_id" in str(e):
                raise Exception("الرقم الوطني موجود مسبقاً")
            else:
                raise Exception(f"خطأ في إضافة الطالب: {str(e)}")
    
    def get_students_by_section(self, section):
        """الحصول على طلاب شعبة معينة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, full_name, national_id, seat_number, birth_date, gender, phone
            FROM students WHERE section = ?
            ORDER BY full_name
        ''', (section,))
        students = cursor.fetchall()
        conn.close()
        return students
    
    def get_all_sections(self):
        """الحصول على جميع الشعب"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT section_name FROM sections ORDER BY section_name")
        sections = [row[0] for row in cursor.fetchall()]
        conn.close()
        return sections
    
    def get_subjects_by_section(self, section):
        """الحصول على مواد شعبة معينة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT s.id, s.subject_name 
            FROM subjects s
            JOIN sections sec ON s.section_id = sec.id
            WHERE sec.section_name = ?
            ORDER BY s.subject_name
        ''', (section,))
        subjects = cursor.fetchall()
        conn.close()
        return subjects
    
    def add_section(self, section_name):
        """إضافة شعبة جديدة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("INSERT INTO sections (section_name) VALUES (?)", (section_name,))
            conn.commit()
            conn.close()
            return True
        except sqlite3.IntegrityError:
            raise Exception("اسم الشعبة موجود مسبقاً")
    
    def add_subject(self, subject_name, section_name):
        """إضافة مادة جديدة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # الحصول على معرف الشعبة
        cursor.execute("SELECT id FROM sections WHERE section_name = ?", (section_name,))
        section_id = cursor.fetchone()
        
        if section_id:
            cursor.execute('''
                INSERT INTO subjects (subject_name, section_id) VALUES (?, ?)
            ''', (subject_name, section_id[0]))
            conn.commit()
        
        conn.close()
    
    def save_grades(self, student_id, subject_id, grades_data, academic_year):
        """حفظ درجات الطالب"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # حساب النتيجة النهائية
        total_work = grades_data['semester1_work'] + grades_data['semester2_work']
        total_exam = grades_data['semester1_exam'] + grades_data['semester2_exam']
        final_total = total_work + total_exam
        
        # شروط النجاح
        is_passed = total_exam >= 24 and final_total >= 50
        
        cursor.execute('''
            INSERT OR REPLACE INTO grades 
            (student_id, subject_id, semester1_work, semester1_exam, 
             semester2_work, semester2_exam, is_passed, academic_year)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            student_id, subject_id,
            grades_data['semester1_work'], grades_data['semester1_exam'],
            grades_data['semester2_work'], grades_data['semester2_exam'],
            is_passed, academic_year
        ))
        
        conn.commit()
        conn.close()
        
        return is_passed
    
    def save_second_round_grade(self, student_id, subject_id, second_round_score):
        """حفظ درجة الدور الثاني"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # الحصول على درجات العمل
        cursor.execute('''
            SELECT semester1_work + semester2_work as total_work
            FROM grades WHERE student_id = ? AND subject_id = ?
        ''', (student_id, subject_id))
        
        result = cursor.fetchone()
        if result:
            total_work = result[0]
            final_score = total_work + second_round_score
            passed_in_second_round = final_score >= 50
            
            cursor.execute('''
                UPDATE grades 
                SET second_round_exam = ?, passed_in_second_round = ?
                WHERE student_id = ? AND subject_id = ?
            ''', (second_round_score, passed_in_second_round, student_id, subject_id))
            
            conn.commit()
            conn.close()
            
            return passed_in_second_round
        
        conn.close()
        return False
    
    def get_student_grades(self, student_id):
        """الحصول على درجات طالب معين"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT s.subject_name, g.semester1_work, g.semester1_exam,
                   g.semester2_work, g.semester2_exam, g.second_round_exam,
                   g.is_passed, g.passed_in_second_round, g.academic_year
            FROM grades g
            JOIN subjects s ON g.subject_id = s.id
            WHERE g.student_id = ?
            ORDER BY s.subject_name
        ''', (student_id,))
        grades = cursor.fetchall()
        conn.close()
        return grades
    
    def search_student(self, search_term):
        """البحث عن طالب"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, full_name, national_id, seat_number, section
            FROM students 
            WHERE full_name LIKE ? OR national_id LIKE ? OR seat_number LIKE ?
        ''', (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))
        results = cursor.fetchall()
        conn.close()
        return results
