#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة الإصلاح السريع لنظام إدارة الطلاب
Quick Fix Tool for Student Management System
"""

import os
import sys
import shutil

def create_missing_files():
    """إنشاء الملفات المفقودة"""
    print("إنشاء الملفات المفقودة...")
    
    # إنشاء ملفات __init__.py
    init_files = [
        'student_management_system/__init__.py',
        'student_management_system/gui/__init__.py',
        'student_management_system/database/__init__.py',
        'student_management_system/utils/__init__.py'
    ]
    
    for file_path in init_files:
        if not os.path.exists(file_path):
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('# Package file\n')
            print(f"✅ تم إنشاء {file_path}")

def fix_permissions():
    """إصلاح صلاحيات المجلدات"""
    print("إصلاح صلاحيات المجلدات...")
    
    try:
        # إنشاء مجلد البيانات
        data_dir = "data"
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
            print(f"✅ تم إنشاء مجلد {data_dir}")
        
        # اختبار الكتابة
        test_file = os.path.join(data_dir, "test.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print("✅ صلاحيات الكتابة تعمل بشكل صحيح")
        
    except Exception as e:
        print(f"❌ خطأ في صلاحيات الكتابة: {e}")
        print("الحل: شغل البرنامج كمدير")

def reset_database():
    """إعادة تعيين قاعدة البيانات"""
    print("إعادة تعيين قاعدة البيانات...")
    
    db_file = "data/student_data.db"
    if os.path.exists(db_file):
        backup_file = f"{db_file}.backup"
        shutil.copy2(db_file, backup_file)
        os.remove(db_file)
        print(f"✅ تم حذف قاعدة البيانات القديمة (نسخة احتياطية: {backup_file})")
    
    try:
        sys.path.append('student_management_system')
        from database.db_manager import DatabaseManager
        
        db = DatabaseManager(db_file)
        print("✅ تم إنشاء قاعدة بيانات جديدة")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

def fix_imports():
    """إصلاح مشاكل الاستيراد"""
    print("إصلاح مشاكل الاستيراد...")
    
    # إضافة المسار إلى sys.path
    project_path = os.path.abspath('student_management_system')
    if project_path not in sys.path:
        sys.path.insert(0, project_path)
    
    # اختبار الاستيرادات
    try:
        from database.db_manager import DatabaseManager
        print("✅ استيراد DatabaseManager")
        
        from utils.arabic_support import get_arabic_font
        print("✅ استيراد arabic_support")
        
        from gui.main_window import MainWindow
        print("✅ استيراد MainWindow")
        
        print("✅ جميع الاستيرادات تعمل بشكل صحيح")
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")

def test_tkinter():
    """اختبار tkinter"""
    print("اختبار tkinter...")
    
    try:
        import tkinter as tk
        from tkinter import messagebox
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.withdraw()
        
        # اختبار رسالة
        result = messagebox.askyesno("اختبار", "هل تظهر هذه الرسالة بشكل صحيح؟")
        
        root.destroy()
        
        if result:
            print("✅ tkinter يعمل بشكل صحيح")
        else:
            print("⚠️  tkinter يعمل لكن قد تكون هناك مشاكل في العرض")
            
    except Exception as e:
        print(f"❌ خطأ في tkinter: {e}")
        print("الحل: أعد تثبيت Python مع tkinter")

def run_system_test():
    """تشغيل اختبار النظام"""
    print("تشغيل اختبار النظام...")
    
    try:
        # تشغيل النظام
        import subprocess
        result = subprocess.run([sys.executable, 'run_student_system.py'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ النظام يبدأ بشكل صحيح")
        else:
            print(f"❌ خطأ في تشغيل النظام: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("✅ النظام يعمل (تم إيقافه بعد 10 ثوان)")
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("أداة الإصلاح السريع لنظام إدارة الطلاب")
    print("=" * 50)
    print()
    
    fixes = [
        ("إنشاء الملفات المفقودة", create_missing_files),
        ("إصلاح صلاحيات المجلدات", fix_permissions),
        ("إصلاح مشاكل الاستيراد", fix_imports),
        ("اختبار tkinter", test_tkinter),
        ("إعادة تعيين قاعدة البيانات", reset_database),
        ("اختبار تشغيل النظام", run_system_test),
    ]
    
    print("سيتم تطبيق الإصلاحات التالية:")
    for i, (name, _) in enumerate(fixes, 1):
        print(f"{i}. {name}")
    
    print("\nهل تريد المتابعة؟ (y/n): ", end="")
    try:
        response = input().lower()
        if response not in ['y', 'yes', 'نعم', 'ن']:
            print("تم الإلغاء")
            return
    except:
        print("تم الإلغاء")
        return
    
    print("\nبدء الإصلاحات...")
    print("-" * 30)
    
    for name, fix_func in fixes:
        print(f"\n{name}:")
        try:
            fix_func()
        except Exception as e:
            print(f"❌ خطأ في {name}: {e}")
    
    print("\n" + "=" * 50)
    print("انتهت عملية الإصلاح")
    print("=" * 50)
    print()
    print("الخطوات التالية:")
    print("1. جرب تشغيل البرنامج مرة أخرى")
    print("2. إذا لم يعمل، شغل أداة التشخيص: python تشخيص_المشاكل.py")
    print("3. إذا استمرت المشاكل، أعد تثبيت Python")
    
    print("\nاضغط Enter للخروج...")
    try:
        input()
    except:
        pass

if __name__ == "__main__":
    main()
